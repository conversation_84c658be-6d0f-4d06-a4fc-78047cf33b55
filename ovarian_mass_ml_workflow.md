# Machine‑Learning & Deep‑Learning Workflow for Predicting the Nature of Ovarian Mass

> **Purpose:** End‑to‑end plan to develop, validate and deploy algorithms that classify ovarian masses as **benign (0)** or **malignant (1)**, integrating the combined dataset and adhering to the study protocol.

---

## 1. Data Audit & Understanding

| Task | Actions | Notes |
|------|---------|-------|
| **Schema inspection** | Load `combined_ovarian_dataset.csv`, inspect data‑types, ranges, and missingness. | Current snapshot ≈ **29 columns × 250 rows**, all numeric. |
| **Variable taxonomy** | Tag features as continuous vs categorical. <ul><li>**Continuous**: CA‑125, HE4, Apo‑A1, FSH, lipid panel (TC, HDL‑c, LDL‑c, TG), urea, creatinine, CEA, prothrombin time, age.</li><li>**Categorical / ordinal**: ultrasound (USG) score, CT stage, “type of mass”, parity, reproductive‑history flags, etc.</li></ul> | Encodings follow ultrasound & CT scoring template in protocol. |
| **Label definition** | Merge redundant `malignant` / `benign` flags → **binary target** (`1 = malignant`, `0 = benign`). | Matches protocol directive for epithelial cancer labelling. |

---

## 2. Data Cleaning & Pre‑processing

1. **Duplicate‑column resolution & unit harmonisation** (already applied).  
2. **Missing values**  
   * *Categorical*: mode imputation or add “unknown” level.  
   * *Continuous*: median imputation or KNN‑based imputation.  
3. **Outlier detection**: robust z‑score / IQR winsorisation (particularly CA‑125, HE4).  
4. **Scaling**  
   * Tree‑based models → none.  
   * Distance‑based / neural nets → z‑score continuous features; leave categories un‑scaled or embed.

Implement as a reusable `sklearn.Pipeline` (Python) or **`recipes`** object (R).

---

## 3. Exploratory Analysis & Feature Engineering

* Check class imbalance (expected malignant ≈ 10 %).  
* Visualise each biomarker vs label (box/violin).  
* Create interaction terms (e.g. CA‑125 × HE4) & clinical indices (ROMA score).  
* Optional: unsupervised **auto‑encoder** to learn non‑linear latent features.

---

## 4. Train / Validation / Test Regime

| Split | Share | Purpose |
|-------|-------|---------|
| Train | 60 % | Model fitting & hyper‑parameter search |
| Validation (5‑fold CV) | 20 % | Model selection & early stopping |
| Hold‑out test | 20 % | Final unbiased performance |

Use **stratified sampling** to preserve benign : malignant ratio.

---

## 5. Baseline & Classical ML Models

| Model | Key Hyper‑parameters | Pros / Cons |
|-------|---------------------|-------------|
| Logistic Regression | penalty (L1/L2), class_weight | Interpretable odds ratios; baseline |
| Random Forest | n_trees, max_depth | Captures non‑linearity; built‑in importance |
| Gradient Boosting (XGBoost / LightGBM) | learning_rate, n_estimators | Often SOTA on tabular biomed data |
| SVM (RBF) | C, γ | Effective for small feature‑sets; needs scaling |

Metrics: **ROC AUC, sensitivity, specificity, F1, PPV, NPV**.

---

## 6. Deep‑Learning Models

| Architecture | Input Handling | Notes |
|--------------|----------------|-------|
| MLP | Embeddings for categorical + scaled continuous | 2‑4 layers, BatchNorm, DropOut; focal BCE loss to up‑weight minority class |
| TabNet / FT‑Transformer | Raw tabular | Feature masks, good interpretability |
| Auto‑encoder → MLP head | Latent features from unsupervised pre‑training | Boosts generalisation with limited samples |

Use early stopping on **validation AUC** and LR decay.

---

## 7. Class‑Imbalance Strategies

* **Cost‑sensitive loss** (`pos_weight` in BCE).  
* **SMOTE‑NC** over‑sampling within CV folds.  
* **Threshold optimisation** (Youden’s J or sensitivity‑priority).

---

## 8. Model Interpretability & Clinical Validation

1. **Global importance**: permutation gain, SHAP summary beeswarm.  
2. **Local explanations**: LIME / SHAP force plots per patient.  
3. **Calibration**: reliability curve & Brier score.  
4. **Decision‑curve analysis**: net benefit across probability thresholds.

---

## 9. Final Model Selection & External Testing

* Pick model with highest validation AUC *and* sensitivity ≥ 0.85.  
* Freeze pipeline → export as **ONNX / TorchScript**.  
* Test on temporal or external hospital cohort; else bootstrap CIs.

---